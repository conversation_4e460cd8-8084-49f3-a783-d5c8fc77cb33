import {
  Card,
  Chip,
  IconCard,
  Modal,
} from '@/shared/components/common';
import { formatDate } from '@/shared/utils/format';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { AgentListItemDto } from '../api/agent.api';
import { useDeleteAgent, useToggleAgentActive } from '../hooks/useAgent';
import { useAiAgentNotification } from '../hooks/useAiAgentNotification';

// Mock data đã được move sang file riêng: ../data/mockAgents.ts

interface AgentCardProps {
  agent: AgentListItemDto;
  /** Callback khi click memories */
  onMemoriesAgent?: (agentId: string) => void;
}

/**
 * Component hiển thị thông tin của một AI Agent
 */
const AgentCard: React.FC<AgentCardProps> = ({ agent, onMemoriesAgent }) => {
  const { t } = useTranslation('aiAgents');
  const navigate = useNavigate();
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const { updateSuccess, updateError, deleteSuccess, deleteError } = useAiAgentNotification();

  // Local state để đảm bảo UI cập nhật ngay lập tức
  const [localActive, setLocalActive] = useState(agent.active);

  // Hooks cho API calls
  const deleteAgentMutation = useDeleteAgent();
  const toggleAgentActiveMutation = useToggleAgentActive();

  // Sync local state với props khi props thay đổi
  React.useEffect(() => {
    setLocalActive(agent.active);
  }, [agent.active]);

  // Sử dụng local state để hiển thị
  const isActive = localActive;

  const handleEditAgent = () => {
    // Navigate đến trang edit agent với typeId
    console.log('AgentCard - handleEditAgent:', { agentId: agent.id, typeId: agent.typeId });
    navigate(`/ai-agents/${agent.id}/edit?typeId=${agent.typeId}`);
  };

  const handleMemoriesClick = () => {
    if (onMemoriesAgent) {
      onMemoriesAgent(agent.id);
    }
  };

  const handleToggleActive = async () => {
    const previousState = isActive;

    try {
      // Cập nhật UI ngay lập tức
      setLocalActive(!isActive);

      // Gọi API với optimistic update
      await toggleAgentActiveMutation.mutateAsync(agent.id);

      updateSuccess(
        previousState
          ? t('notification.titles.deactivateSuccess', 'Agent đã được bật')
          : t('notification.titles.activateSuccess', 'Agent đã được tắt')
      );
    } catch (error) {
      console.error('Error toggling agent active:', error);

      // Rollback local state nếu API thất bại
      setLocalActive(previousState);

      updateError(
        'Agent',
        error instanceof Error
          ? error.message
          : t('aiAgents.toggleError', 'Có lỗi xảy ra khi thay đổi trạng thái agent')
      );
    }
  };

  const handleDeleteClick = () => {
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      await deleteAgentMutation.mutateAsync(agent.id);
      setShowDeleteModal(false);

      deleteSuccess('Agent');
    } catch (error) {
      console.error('Error deleting agent:', error);
      deleteError('Agent', error instanceof Error ? error.message : undefined);
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteModal(false);
  };

  // Tính toán phần trăm kinh nghiệm từ dữ liệu thực
  const currentExp = parseInt(agent.exp) || 0;
  const maxExp = parseInt(agent.expMax) || 1;
  const experiencePercent = Math.round((currentExp / maxExp) * 100);

  // Format ngày tạo
  const createdDate = formatDate(agent.createdAt, 'DD/MM/YYYY');

  return (
    <>
      <Card
        className="h-full overflow-hidden border border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600 transition-all duration-300 bg-white dark:bg-gray-800"
        variant="bordered"
      >
        <div className="p-5">
          {/* Header Section - Avatar và thông tin chính */}
          <div className="flex items-start gap-4 mb-4">
            {/* Avatar với badge overlay */}
            <div className="relative flex-shrink-0">
              <div className="w-16 h-16 rounded-xl overflow-hidden bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-700 dark:to-gray-600 p-1">
                <img
                  src={agent.avatar || '/assets/images/default-avatar.png'}
                  alt={agent.name}
                  className="w-full h-full rounded-lg object-cover"
                />
              </div>

              {/* Badge overlay - sử dụng badgeUrl */}
              {agent.badgeUrl && (
                <div className="absolute -top-1 -right-1 w-6 h-6">
                  <img
                    src={agent.badgeUrl}
                    alt="Level badge"
                    className="w-full h-full object-contain"
                  />
                </div>
              )}

              {/* Status indicator */}
              <div className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white dark:border-gray-800 ${
                isActive ? 'bg-green-500' : 'bg-gray-400'
              }`} />
            </div>

            {/* Agent Info */}
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between gap-2">
                <div className="min-w-0 flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white truncate mb-1">
                    {agent.name}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                    {agent.typeName}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-500">
                    {createdDate}
                  </p>
                </div>

                {/* Model chip */}
                <div className="flex-shrink-0">
                  <Chip
                    variant="primary"
                    size="sm"
                    className="font-medium text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                  >
                    {agent.modelId}
                  </Chip>
                </div>
              </div>
            </div>
          </div>

          {/* Level & Experience Section */}
          <div className="mb-4">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Level {agent.level}
                </span>
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {currentExp}/{maxExp} EXP
                </span>
              </div>
              <div className="flex items-center gap-1">
                <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                  {experiencePercent}%
                </span>
              </div>
            </div>

            {/* Progress bar */}
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div
                className={`h-2 rounded-full transition-all duration-300 ${
                  experiencePercent >= 80
                    ? 'bg-gradient-to-r from-green-400 to-green-600'
                    : experiencePercent >= 50
                      ? 'bg-gradient-to-r from-yellow-400 to-orange-500'
                      : 'bg-gradient-to-r from-blue-400 to-blue-600'
                }`}
                style={{ width: `${experiencePercent}%` }}
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center justify-between pt-3 border-t border-gray-100 dark:border-gray-700">
            <div className="flex items-center gap-3">
              {/* Power toggle */}
              <button
                onClick={handleToggleActive}
                disabled={toggleAgentActiveMutation.isPending}
                className={`p-2 rounded-lg transition-all duration-200 ${
                  isActive
                    ? 'bg-green-100 text-green-600 hover:bg-green-200 dark:bg-green-900 dark:text-green-400 dark:hover:bg-green-800'
                    : 'bg-gray-100 text-gray-400 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-500 dark:hover:bg-gray-600'
                }`}
                title={isActive ? t('common.deactivate') : t('common.activate')}
              >
                <IconCard icon="power" size="sm" variant="ghost" />
              </button>

              {/* Memories */}
              <button
                onClick={handleMemoriesClick}
                className="p-2 rounded-lg bg-gray-100 text-gray-600 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-400 dark:hover:bg-gray-600 transition-all duration-200"
                title={t('aiAgents:memories.title', 'Memories')}
              >
                <IconCard icon="clock" size="sm" variant="ghost" />
              </button>
            </div>

            <div className="flex items-center gap-2">
              {/* Edit */}
              <button
                onClick={handleEditAgent}
                className="p-2 rounded-lg bg-blue-100 text-blue-600 hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-400 dark:hover:bg-blue-800 transition-all duration-200"
                title={t('common.edit')}
              >
                <IconCard icon="edit" size="sm" variant="ghost" />
              </button>

              {/* Delete */}
              <button
                onClick={handleDeleteClick}
                disabled={deleteAgentMutation.isPending}
                className="p-2 rounded-lg bg-red-100 text-red-600 hover:bg-red-200 dark:bg-red-900 dark:text-red-400 dark:hover:bg-red-800 transition-all duration-200"
                title={t('common.delete')}
              >
                <IconCard icon="trash" size="sm" variant="ghost" />
              </button>
            </div>
          </div>
        </div>
      </Card>

      {/* Modal xác nhận xóa */}
      <Modal
        isOpen={showDeleteModal}
        onClose={handleDeleteCancel}
        title={t('deleteConfirmTitle', 'Xác nhận xóa Agent')}
        size="md"
        footer={
          <div className="flex justify-end gap-3">

            <IconCard
              icon="trash"
              title={t('common.delete', 'Xóa')}
              onClick={handleDeleteConfirm}
              variant="danger"
              disabled={deleteAgentMutation.isPending}
            />

          </div>
        }
      >
        <div className="space-y-4">
          

          <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
            <div className="flex items-center gap-3">
              <img
                src={agent.avatar || '/assets/images/default-avatar.png'}
                alt={agent.name}
                className="w-10 h-10 rounded-full"
              />
              <div>
                <p className="font-medium text-gray-900 dark:text-white">{agent.name}</p>
                <p className="text-sm text-gray-500 dark:text-gray-400">{agent.typeName}</p>
              </div>
            </div>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default AgentCard;

// Mock data đã được move sang file riêng: ../data/mockAgents.ts
// AgentCardDemo component đã được move sang file riêng để tránh fast refresh warning
