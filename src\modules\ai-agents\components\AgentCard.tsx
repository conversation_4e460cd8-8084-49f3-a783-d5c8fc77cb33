import {
  Card,
  Chip,
  IconCard,
  Modal,
} from '@/shared/components/common';
import { formatDate } from '@/shared/utils/format';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { AgentListItemDto } from '../api/agent.api';
import { useDeleteAgent, useToggleAgentActive } from '../hooks/useAgent';
import { useAiAgentNotification } from '../hooks/useAiAgentNotification';

// Mock data đã được move sang file riêng: ../data/mockAgents.ts

interface AgentCardProps {
  agent: AgentListItemDto;
  /** Callback khi click memories */
  onMemoriesAgent?: (agentId: string) => void;
}

/**
 * Component hiển thị thông tin của một AI Agent
 */
const AgentCard: React.FC<AgentCardProps> = ({ agent, onMemoriesAgent }) => {
  const { t } = useTranslation('aiAgents');
  const navigate = useNavigate();
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const { updateSuccess, updateError, deleteSuccess, deleteError } = useAiAgentNotification();

  // Local state để đảm bảo UI cập nhật ngay lập tức
  const [localActive, setLocalActive] = useState(agent.active);

  // Hooks cho API calls
  const deleteAgentMutation = useDeleteAgent();
  const toggleAgentActiveMutation = useToggleAgentActive();

  // Sync local state với props khi props thay đổi
  React.useEffect(() => {
    setLocalActive(agent.active);
  }, [agent.active]);

  // Sử dụng local state để hiển thị
  const isActive = localActive;

  const handleEditAgent = () => {
    // Navigate đến trang edit agent với typeId
    console.log('AgentCard - handleEditAgent:', { agentId: agent.id, typeId: agent.typeId });
    navigate(`/ai-agents/${agent.id}/edit?typeId=${agent.typeId}`);
  };

  const handleMemoriesClick = () => {
    if (onMemoriesAgent) {
      onMemoriesAgent(agent.id);
    }
  };

  const handleToggleActive = async () => {
    const previousState = isActive;

    try {
      // Cập nhật UI ngay lập tức
      setLocalActive(!isActive);

      // Gọi API với optimistic update
      await toggleAgentActiveMutation.mutateAsync(agent.id);

      updateSuccess(
        previousState
          ? t('notification.titles.deactivateSuccess', 'Agent đã được bật')
          : t('notification.titles.activateSuccess', 'Agent đã được tắt')
      );
    } catch (error) {
      console.error('Error toggling agent active:', error);

      // Rollback local state nếu API thất bại
      setLocalActive(previousState);

      updateError(
        'Agent',
        error instanceof Error
          ? error.message
          : t('aiAgents.toggleError', 'Có lỗi xảy ra khi thay đổi trạng thái agent')
      );
    }
  };

  const handleDeleteClick = () => {
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      await deleteAgentMutation.mutateAsync(agent.id);
      setShowDeleteModal(false);

      deleteSuccess('Agent');
    } catch (error) {
      console.error('Error deleting agent:', error);
      deleteError('Agent', error instanceof Error ? error.message : undefined);
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteModal(false);
  };

  // Tính toán phần trăm kinh nghiệm từ dữ liệu thực
  const currentExp = parseInt(agent.exp) || 0;
  const maxExp = parseInt(agent.expMax) || 1;
  const experiencePercent = Math.round((currentExp / maxExp) * 100);

  // Format ngày tạo
  const createdDate = formatDate(agent.createdAt, 'DD/MM/YYYY');

  return (
    <>
      <Card
        className="group relative h-full overflow-hidden bg-gradient-to-br from-white via-gray-50 to-gray-100 dark:from-gray-800 dark:via-gray-850 dark:to-gray-900 border-0 shadow-lg hover:shadow-xl transition-all duration-500 hover:scale-[1.02]"
        variant="elevated"
      >
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5 dark:opacity-10">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-400 via-purple-500 to-pink-500" />
        </div>

        <div className="relative p-6">
          {/* Header Section */}
          <div className="flex items-center gap-4 mb-6">
            {/* Avatar Container */}
            <div className="relative">
              {/* Outer Ring with Gradient */}
              <div className="relative w-20 h-20 rounded-full p-1 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 shadow-lg">
                {/* Inner Ring */}
                <div className="w-full h-full rounded-full p-1 bg-white dark:bg-gray-800">
                  {/* Avatar */}
                  <div className="w-full h-full rounded-full overflow-hidden bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-600">
                    <img
                      src={agent.avatar || '/assets/images/default-avatar.png'}
                      alt={agent.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                </div>
              </div>

              {/* Badge Overlay */}
              {agent.badgeUrl && (
                <div className="absolute -top-2 -right-2 w-8 h-8 bg-white dark:bg-gray-800 rounded-full p-1 shadow-lg">
                  <img
                    src={agent.badgeUrl}
                    alt="Level badge"
                    className="w-full h-full object-contain"
                  />
                </div>
              )}

              {/* Status Indicator */}
              <div className={`absolute bottom-0 right-0 w-5 h-5 rounded-full border-3 border-white dark:border-gray-800 shadow-md ${
                isActive
                  ? 'bg-gradient-to-r from-green-400 to-green-600'
                  : 'bg-gradient-to-r from-gray-400 to-gray-500'
              }`}>
                {isActive && (
                  <div className="absolute inset-1 bg-white rounded-full animate-pulse" />
                )}
              </div>
            </div>

            {/* Agent Info */}
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between gap-3">
                <div className="min-w-0 flex-1">
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white truncate mb-1 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                    {agent.name}
                  </h3>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-300 mb-1">
                    {agent.typeName}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400 flex items-center gap-1">
                    <span className="w-1 h-1 bg-gray-400 rounded-full"></span>
                    {createdDate}
                  </p>
                </div>

                {/* Model Chip */}
                <div className="flex-shrink-0">
                  <div className="px-3 py-1 bg-gradient-to-r from-blue-500 to-purple-600 text-white text-xs font-semibold rounded-full shadow-md">
                    {agent.modelId}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Level & Experience Section */}
          <div className="mb-6">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-3">
                <div className="flex items-center gap-2">
                  <div className="w-6 h-6 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs font-bold">{agent.level}</span>
                  </div>
                  <span className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                    Level {agent.level}
                  </span>
                </div>
                <span className="text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-full">
                  {currentExp}/{maxExp} EXP
                </span>
              </div>
              <div className="text-right">
                <span className="text-lg font-bold text-gray-800 dark:text-gray-200">
                  {experiencePercent}%
                </span>
              </div>
            </div>

            {/* Enhanced Progress Bar */}
            <div className="relative">
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 shadow-inner">
                <div
                  className={`h-3 rounded-full transition-all duration-700 ease-out relative overflow-hidden ${
                    experiencePercent >= 80
                      ? 'bg-gradient-to-r from-green-400 via-green-500 to-green-600'
                      : experiencePercent >= 50
                        ? 'bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500'
                        : 'bg-gradient-to-r from-blue-400 via-blue-500 to-purple-600'
                  }`}
                  style={{ width: `${experiencePercent}%` }}
                >
                  {/* Shimmer Effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-30 animate-pulse" />
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-2">
              {/* Power Toggle */}
              <button
                onClick={handleToggleActive}
                disabled={toggleAgentActiveMutation.isPending}
                className={`relative p-3 rounded-xl transition-all duration-300 transform hover:scale-110 ${
                  isActive
                    ? 'bg-gradient-to-r from-green-500 to-green-600 text-white shadow-lg shadow-green-500/30'
                    : 'bg-gradient-to-r from-gray-400 to-gray-500 text-white shadow-lg shadow-gray-500/30'
                } hover:shadow-xl`}
                title={isActive ? t('common.deactivate') : t('common.activate')}
              >
                <IconCard icon="power" size="sm" variant="ghost" />
                {isActive && (
                  <div className="absolute inset-0 bg-white opacity-20 rounded-xl animate-ping" />
                )}
              </button>

              {/* Memories */}
              <button
                onClick={handleMemoriesClick}
                className="p-3 rounded-xl bg-gradient-to-r from-indigo-500 to-purple-600 text-white shadow-lg shadow-indigo-500/30 hover:shadow-xl transition-all duration-300 transform hover:scale-110"
                title={t('aiAgents:memories.title', 'Memories')}
              >
                <IconCard icon="clock" size="sm" variant="ghost" />
              </button>
            </div>

            <div className="flex items-center gap-2">
              {/* Edit */}
              <button
                onClick={handleEditAgent}
                className="p-3 rounded-xl bg-gradient-to-r from-blue-500 to-cyan-600 text-white shadow-lg shadow-blue-500/30 hover:shadow-xl transition-all duration-300 transform hover:scale-110"
                title={t('common.edit')}
              >
                <IconCard icon="edit" size="sm" variant="ghost" />
              </button>

              {/* Delete */}
              <button
                onClick={handleDeleteClick}
                disabled={deleteAgentMutation.isPending}
                className="p-3 rounded-xl bg-gradient-to-r from-red-500 to-pink-600 text-white shadow-lg shadow-red-500/30 hover:shadow-xl transition-all duration-300 transform hover:scale-110"
                title={t('common.delete')}
              >
                <IconCard icon="trash" size="sm" variant="ghost" />
              </button>
            </div>
          </div>
        </div>
      </Card>

      {/* Modal xác nhận xóa */}
      <Modal
        isOpen={showDeleteModal}
        onClose={handleDeleteCancel}
        title={t('deleteConfirmTitle', 'Xác nhận xóa Agent')}
        size="md"
        footer={
          <div className="flex justify-end gap-3">

            <IconCard
              icon="trash"
              title={t('common.delete', 'Xóa')}
              onClick={handleDeleteConfirm}
              variant="danger"
              disabled={deleteAgentMutation.isPending}
            />

          </div>
        }
      >
        <div className="space-y-4">
          

          <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
            <div className="flex items-center gap-3">
              <img
                src={agent.avatar || '/assets/images/default-avatar.png'}
                alt={agent.name}
                className="w-10 h-10 rounded-full"
              />
              <div>
                <p className="font-medium text-gray-900 dark:text-white">{agent.name}</p>
                <p className="text-sm text-gray-500 dark:text-gray-400">{agent.typeName}</p>
              </div>
            </div>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default AgentCard;

// Mock data đã được move sang file riêng: ../data/mockAgents.ts
// AgentCardDemo component đã được move sang file riêng để tránh fast refresh warning
